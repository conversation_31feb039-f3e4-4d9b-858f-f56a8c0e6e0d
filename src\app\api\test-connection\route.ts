import { NextRequest } from 'next/server'

import { successResponse } from '@/lib/api-utils'
import { supabase } from '@/lib/supabase'

export async function GET(_request: NextRequest) {
  const diagnostics = {
    timestamp: new Date().toISOString(),
    environment: {
      nodeEnv: process.env.NODE_ENV,
      url: process.env.NEXT_PUBLIC_SUPABASE_URL ? 'configured' : 'missing',
      urlValue: process.env.NEXT_PUBLIC_SUPABASE_URL?.substring(0, 30) + '...',
      key: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY ? 'configured' : 'missing',
      keyLength: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY?.length || 0
    },
    tests: [] as any[]
  }

  try {
    console.log('🔍 Starting comprehensive connection diagnostics...')

    // Test 1: Environment variables
    console.log('🔍 Test 1: Environment variables')
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL) {
      diagnostics.tests.push({
        name: 'Environment Variables',
        status: 'failed',
        error: 'NEXT_PUBLIC_SUPABASE_URL is not set'
      })
      return successResponse(diagnostics)
    }

    if (!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      diagnostics.tests.push({
        name: 'Environment Variables',
        status: 'failed',
        error: 'NEXT_PUBLIC_SUPABASE_ANON_KEY is not set'
      })
      return successResponse(diagnostics)
    }

    diagnostics.tests.push({
      name: 'Environment Variables',
      status: 'passed',
      message: 'All required environment variables are set'
    })

    // Test 2: Basic HTTP connectivity to Supabase
    console.log('🔍 Test 2: Basic HTTP connectivity')
    try {
      const baseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
      const response = await fetch(`${baseUrl}/rest/v1/`, {
        method: 'HEAD',
        headers: {
          'apikey': process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
          'Authorization': `Bearer ${process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!}`
        }
      })

      diagnostics.tests.push({
        name: 'HTTP Connectivity',
        status: response.ok ? 'passed' : 'failed',
        message: `HTTP ${response.status}: ${response.statusText}`,
        details: {
          status: response.status,
          statusText: response.statusText,
          headers: Object.fromEntries(response.headers.entries())
        }
      })
    } catch (httpError) {
      diagnostics.tests.push({
        name: 'HTTP Connectivity',
        status: 'failed',
        error: httpError instanceof Error ? httpError.message : 'Unknown HTTP error',
        details: {
          errorType: httpError instanceof Error ? httpError.name : 'Unknown',
          errorMessage: httpError instanceof Error ? httpError.message : 'Unknown error'
        }
      })
    }

    // Test 3: Supabase client connection
    console.log('🔍 Test 3: Supabase client connection')
    try {
      const { error } = await supabase
        .from('products')
        .select('count(*)', { count: 'exact', head: true })

      if (error) {
        diagnostics.tests.push({
          name: 'Supabase Client',
          status: 'failed',
          error: error.message,
          details: error
        })
      } else {
        diagnostics.tests.push({
          name: 'Supabase Client',
          status: 'passed',
          message: 'Successfully connected to Supabase and queried products table'
        })
      }
    } catch (supabaseError) {
      diagnostics.tests.push({
        name: 'Supabase Client',
        status: 'failed',
        error: supabaseError instanceof Error ? supabaseError.message : 'Unknown Supabase error',
        details: {
          errorType: supabaseError instanceof Error ? supabaseError.name : 'Unknown',
          errorMessage: supabaseError instanceof Error ? supabaseError.message : 'Unknown error'
        }
      })
    }

    // Test 4: Network diagnostics
    console.log('🔍 Test 4: Network diagnostics')
    try {
      const testUrl = 'https://httpbin.org/get'
      const networkResponse = await fetch(testUrl, {
        method: 'GET',
        signal: AbortSignal.timeout(5000)
      })

      diagnostics.tests.push({
        name: 'Network Connectivity',
        status: networkResponse.ok ? 'passed' : 'failed',
        message: `External network test: ${networkResponse.status}`,
        details: {
          testUrl,
          status: networkResponse.status,
          statusText: networkResponse.statusText
        }
      })
    } catch (networkError) {
      diagnostics.tests.push({
        name: 'Network Connectivity',
        status: 'failed',
        error: networkError instanceof Error ? networkError.message : 'Network test failed',
        details: {
          errorType: networkError instanceof Error ? networkError.name : 'Unknown'
        }
      })
    }

    console.log('✅ Diagnostics completed')
    return successResponse(diagnostics)

  } catch (error) {
    console.error('❌ Diagnostic error:', error)
    diagnostics.tests.push({
      name: 'Diagnostic Process',
      status: 'failed',
      error: error instanceof Error ? error.message : 'Unknown diagnostic error'
    })

    return successResponse(diagnostics)
  }
}
