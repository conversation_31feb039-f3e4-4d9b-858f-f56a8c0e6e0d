/**
 * Professional Logging Utility for Revantad Store
 * 
 * Provides structured logging with different levels and proper formatting.
 * Automatically handles development vs production environments.
 */

type LogLevel = 'info' | 'warn' | 'error' | 'debug'

interface LogContext {
  component?: string
  action?: string
  userId?: string
  timestamp?: string
  [key: string]: any
}

class Logger {
  private isDevelopment = process.env.NODE_ENV === 'development'
  private isClient = typeof window !== 'undefined'

  private formatMessage(level: LogLevel, message: string, context?: LogContext): string {
    const timestamp = new Date().toISOString()
    const prefix = this.getPrefix(level)
    
    if (context) {
      const contextStr = Object.entries(context)
        .map(([key, value]) => `${key}=${JSON.stringify(value)}`)
        .join(' ')
      return `${prefix} [${timestamp}] ${message} | ${contextStr}`
    }
    
    return `${prefix} [${timestamp}] ${message}`
  }

  private getPrefix(level: LogLevel): string {
    const prefixes = {
      info: 'ℹ️',
      warn: '⚠️',
      error: '❌',
      debug: '🔍'
    }
    return prefixes[level] || 'ℹ️'
  }

  /**
   * Log informational messages (development only)
   */
  info(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      const formatted = this.formatMessage('info', message, context)
      console.log(formatted)
    }
  }

  /**
   * Log warning messages (always shown)
   */
  warn(message: string, context?: LogContext): void {
    const formatted = this.formatMessage('warn', message, context)
    console.warn(formatted)
  }

  /**
   * Log error messages (always shown)
   */
  error(message: string, error?: Error | any, context?: LogContext): void {
    const formatted = this.formatMessage('error', message, context)
    console.error(formatted, error)
  }

  /**
   * Log debug messages (development only)
   */
  debug(message: string, context?: LogContext): void {
    if (this.isDevelopment) {
      const formatted = this.formatMessage('debug', message, context)
      console.log(formatted)
    }
  }

  /**
   * Log API requests and responses
   */
  api(method: string, url: string, status?: number, duration?: number): void {
    if (this.isDevelopment) {
      const context = { method, url, status, duration: duration ? `${duration}ms` : undefined }
      this.info('API Request', context)
    }
  }

  /**
   * Log database operations
   */
  db(operation: string, table: string, duration?: number, recordCount?: number): void {
    if (this.isDevelopment) {
      const context = { 
        operation, 
        table, 
        duration: duration ? `${duration}ms` : undefined,
        recordCount 
      }
      this.info('Database Operation', context)
    }
  }

  /**
   * Log user actions for analytics
   */
  userAction(action: string, component: string, userId?: string, metadata?: any): void {
    if (this.isDevelopment) {
      const context = { action, component, userId, ...metadata }
      this.info('User Action', context)
    }
  }

  /**
   * Log performance metrics
   */
  performance(metric: string, value: number, unit: string = 'ms'): void {
    if (this.isDevelopment) {
      const context = { metric, value: `${value}${unit}` }
      this.info('Performance Metric', context)
    }
  }
}

// Export singleton instance
export const logger = new Logger()

// Export convenience functions for common use cases
export const logInfo = (message: string, context?: LogContext) => logger.info(message, context)
export const logWarn = (message: string, context?: LogContext) => logger.warn(message, context)
export const logError = (message: string, error?: Error | any, context?: LogContext) => logger.error(message, error, context)
export const logDebug = (message: string, context?: LogContext) => logger.debug(message, context)
export const logApi = (method: string, url: string, status?: number, duration?: number) => logger.api(method, url, status, duration)
export const logDb = (operation: string, table: string, duration?: number, recordCount?: number) => logger.db(operation, table, duration, recordCount)
export const logUserAction = (action: string, component: string, userId?: string, metadata?: any) => logger.userAction(action, component, userId, metadata)
export const logPerformance = (metric: string, value: number, unit?: string) => logger.performance(metric, value, unit)

export default logger
